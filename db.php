<?php
// Database Configuration for CRM Pro
$host = 'localhost';          // XAMPP usually uses 'localhost'
$username = 'root';          // Default XAMPP username
$password = '';              // Default XAMPP password is empty
$database = 'crm_pro';       // Your database name
$port = 3306;                // Default MySQL port

// Global database connection
$conn = null;
$pdo = null;
$db_type = null;
$db_error = null;

try {
    // Try PDO first (more widely available)
    if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
        $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        $conn = $pdo;
        $db_type = 'pdo';
    } 
    // Fallback to mysqli if available
    elseif (extension_loaded('mysqli')) {
        $conn = new mysqli($host, $username, $password, $database, $port);
        $conn->set_charset("utf8mb4");
        
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        $db_type = 'mysqli';
    } 
    // No database extensions available
    else {
        throw new Exception("Neither PDO nor mysqli extensions are available. Please enable one of them in php.ini");
    }
    
    // Create users table if it doesn't exist
    createUsersTableIfNotExists();
    
} catch (Exception $e) {
    // Don't die immediately, let the setup page handle it
    $conn = null;
    $db_error = $e->getMessage();
}

function createUsersTableIfNotExists() {
    global $conn, $db_type;
    
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('superadmin', 'admin', 'user') DEFAULT 'user',
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    try {
        if ($db_type === 'pdo') {
            $conn->exec($sql);
        } else {
            $conn->query($sql);
        }
        
        // Create default admin user if no users exist
        createDefaultAdminUser();
    } catch (Exception $e) {
        error_log("Table creation error: " . $e->getMessage());
    }
}

function createDefaultAdminUser() {
    global $conn, $db_type;
    
    // Check if any users exist
    $check_sql = "SELECT COUNT(*) as count FROM users";
    $count = 0;
    
    try {
        if ($db_type === 'pdo') {
            $stmt = $conn->query($check_sql);
            $result = $stmt->fetch();
            $count = $result['count'];
        } else {
            $result = $conn->query($check_sql);
            $row = $result->fetch_assoc();
            $count = $row['count'];
        }
        
        // If no users exist, create default admin
        if ($count == 0) {
            $username = 'admin';
            $password = 'admin123'; // Default password, should be changed after first login
            $hashed_password = password_hash($password, PASSWORD_BCRYPT);
            
            $insert_sql = "INSERT INTO users (username, password_hash, full_name, role) 
                           VALUES (?, ?, ?, ?)";
            
            if ($db_type === 'pdo') {
                $stmt = $conn->prepare($insert_sql);
                $stmt->execute([$username, $hashed_password, 'Administrator', 'admin']);
            } else {
                $stmt = $conn->prepare($insert_sql);
                $stmt->bind_param("ssss", $username, $hashed_password, 'Administrator', 'admin');
                $stmt->execute();
            }
        }
    } catch (Exception $e) {
        error_log("Default user creation error: " . $e->getMessage());
    }
}

// ==================== USER FUNCTIONS ====================

function authenticateUser($username, $password) {
    global $conn, $db_type;
    
    if (!$conn) {
        error_log("Database connection not available");
        return false;
    }
    
    try {
        if ($db_type === 'pdo') {
            $stmt = $conn->prepare("SELECT id, username, password_hash, role, full_name FROM users WHERE username = ? AND status = 'active'");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
        } else {
            $stmt = $conn->prepare("SELECT id, username, password_hash, role, full_name FROM users WHERE username = ? AND status = 'active'");
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
        }
        
        if ($user && password_verify($password, $user['password_hash'])) {
            // Update last login
            if ($db_type === 'pdo') {
                $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);
            } else {
                $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->bind_param("i", $user['id']);
                $stmt->execute();
            }
            return $user;
        }
    } catch (Exception $e) {
        error_log("Authentication error: " . $e->getMessage());
        return false;
    }
    
    return false;
}

// ==================== LEAD FUNCTIONS ====================

function getLeads($user_id = null, $role = null) {
    global $conn, $db_type;
    
    if (!$conn) return [];
    
    try {
        $sql = "SELECT l.*, u1.full_name as assigned_to_name, u2.full_name as created_by_name 
                FROM leads l 
                LEFT JOIN users u1 ON l.assigned_to = u1.id 
                LEFT JOIN users u2 ON l.created_by = u2.id";
        
        $params = [];
        
        if ($role === 'user' && $user_id) {
            $sql .= " WHERE l.assigned_to = ? OR l.created_by = ?";
            $params = [$user_id, $user_id];
        }
        
        $sql .= " ORDER BY l.created_at DESC";
        
        if ($db_type === 'pdo') {
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } else {
            if (!empty($params)) {
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ii", ...$params);
                $stmt->execute();
                $result = $stmt->get_result();
            } else {
                $result = $conn->query($sql);
            }
            
            $leads = [];
            while ($row = $result->fetch_assoc()) {
                $leads[] = $row;
            }
            return $leads;
        }
    } catch (Exception $e) {
        error_log("Get leads error: " . $e->getMessage());
        return [];
    }
}

function addLead($lead_data) {
    global $conn, $db_type;
    
    if (!$conn) return false;
    
    try {
        $assigned_to = $lead_data['assigned_to'] ?? 1; // Default to admin
        $created_by = $lead_data['created_by'] ?? 1; // Default to admin
        $estimated_value = $lead_data['estimated_value'] ?? 0.00;
        $priority = $lead_data['priority'] ?? 'medium';
        
        $sql = "INSERT INTO leads (name, email, phone, company, service, status, source, priority, assigned_to, created_by, notes, estimated_value) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        if ($db_type === 'pdo') {
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $lead_data['name'],
                $lead_data['email'],
                $lead_data['phone'],
                $lead_data['company'],
                $lead_data['service'],
                $lead_data['status'],
                $lead_data['source'],
                $priority,
                $assigned_to,
                $created_by,
                $lead_data['notes'],
                $estimated_value
            ]);
            return $conn->lastInsertId();
        } else {
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssssssiisd", 
                $lead_data['name'],
                $lead_data['email'],
                $lead_data['phone'],
                $lead_data['company'],
                $lead_data['service'],
                $lead_data['status'],
                $lead_data['source'],
                $priority,
                $assigned_to,
                $created_by,
                $lead_data['notes'],
                $estimated_value
            );
            
            if ($stmt->execute()) {
                return $conn->insert_id;
            }
        }
    } catch (Exception $e) {
        error_log("Add lead error: " . $e->getMessage());
        return false;
    }
    
    return false;
}

// ==================== OTHER FUNCTIONS ====================

function getDashboardStats() {
    global $conn, $db_type;
    
    if (!$conn) {
        return [
            'total_leads' => 0,
            'new_leads' => 0,
            'hot_leads' => 0,
            'converted_leads' => 0,
            'monthly_data' => []
        ];
    }
    
    try {
        $stats = [];
        
        if ($db_type === 'pdo') {
            $stmt = $conn->query("SELECT COUNT(*) as total FROM leads");
            $stats['total_leads'] = $stmt->fetch()['total'];
            
            $stmt = $conn->query("SELECT COUNT(*) as total FROM leads WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
            $stats['new_leads'] = $stmt->fetch()['total'];
            
            $stmt = $conn->query("SELECT COUNT(*) as total FROM leads WHERE status = 'hot'");
            $stats['hot_leads'] = $stmt->fetch()['total'];
            
            $stmt = $conn->query("SELECT COUNT(*) as total FROM leads WHERE status = 'converted'");
            $stats['converted_leads'] = $stmt->fetch()['total'];
        } else {
            $result = $conn->query("SELECT COUNT(*) as total FROM leads");
            $stats['total_leads'] = $result->fetch_assoc()['total'];
            
            $result = $conn->query("SELECT COUNT(*) as total FROM leads WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
            $stats['new_leads'] = $result->fetch_assoc()['total'];
            
            $result = $conn->query("SELECT COUNT(*) as total FROM leads WHERE status = 'hot'");
            $stats['hot_leads'] = $result->fetch_assoc()['total'];
            
            $result = $conn->query("SELECT COUNT(*) as total FROM leads WHERE status = 'converted'");
            $stats['converted_leads'] = $result->fetch_assoc()['total'];
        }
        
        $stats['monthly_data'] = [];
        return $stats;
        
    } catch (Exception $e) {
        error_log("Dashboard stats error: " . $e->getMessage());
        return [
            'total_leads' => 0,
            'new_leads' => 0,
            'hot_leads' => 0,
            'converted_leads' => 0,
            'monthly_data' => []
        ];
    }
}

// Placeholder functions for compatibility
function updateLead($id, $lead_data) { return true; }
function deleteLead($id) { return true; }
function getLeadById($id) { return null; }
function getUserById($id) { return null; }
function getUserIdByUsername($username) { return 1; } // Default to admin
function getServices() { return []; }
function getSetting($key, $default = null) { return $default; }
function setSetting($key, $value, $type = 'string') { return true; }