<?php
// Database Configuration for CRM Pro - Simple Procedural PHP
$host = 'localhost';
$username = 'root';          // Change this to your MySQL username
$password = '';              // Change this to your MySQL password
$database = 'crm_pro';
$port = 3306;

// Global database connection
$conn = null;
$db_error = null;

// Simple MySQL connection using PDO (procedural style)
try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $conn = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (PDOException $e) {
    $db_error = "Connection failed: " . $e->getMessage();
    error_log("Database connection error: " . $e->getMessage());
    $conn = null;
}

// ==================== USER AUTHENTICATION FUNCTIONS ====================

function authenticateUser($username, $password) {
    global $conn;
    
    if (!$conn) return false;
    
    // Demo passwords for testing (you can modify these)
    $demo_passwords = [
        'superadmin' => 'super123',
        'Suraj' => 'Suraj',
        'admin' => 'admin123',
        'user' => 'user123'
    ];
    
    // Check if username exists in demo passwords
    if (isset($demo_passwords[$username]) && $demo_passwords[$username] === $password) {
        // Create a user array with demo data
        $user = [
            'id' => 1,
            'username' => $username,
            'role' => ($username === 'superadmin') ? 'superadmin' : (($username === 'Suraj' || $username === 'admin') ? 'admin' : 'user'),
            'full_name' => ucfirst($username),
            'email' => $username . '@crmPro.com'
        ];
        
        // Update last login (if table exists)
        $update_query = "UPDATE users SET last_login = NOW() WHERE username = '$username'";
        mysqli_query($conn, $update_query);
        
        return $user;
    }
    
    return false;
}

// ==================== LEAD MANAGEMENT FUNCTIONS ====================

function getLeads($user_id = null, $role = 'user') {
    global $conn;

    if (!$conn) return [];

    try {
        $query = "SELECT * FROM leads ORDER BY created_at DESC";
        $stmt = $conn->query($query);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting leads: " . $e->getMessage());
        return [];
    }
}

function addLead($lead_data) {
    global $conn;

    if (!$conn) return false;

    try {
        $query = "INSERT INTO leads (name, email, phone, company, service, status, source, priority, notes, estimated_value, created_at)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $conn->prepare($query);
        $result = $stmt->execute([
            $lead_data['name'] ?? '',
            $lead_data['email'] ?? '',
            $lead_data['phone'] ?? '',
            $lead_data['company'] ?? '',
            $lead_data['service'] ?? '',
            $lead_data['status'] ?? 'new',
            $lead_data['source'] ?? 'manual',
            $lead_data['priority'] ?? 'medium',
            $lead_data['notes'] ?? '',
            floatval($lead_data['estimated_value'] ?? 0)
        ]);

        if ($result) {
            return $conn->lastInsertId();
        }

        return false;
    } catch (PDOException $e) {
        error_log("Error adding lead: " . $e->getMessage());
        return false;
    }
}

function updateLead($lead_id, $lead_data) {
    global $conn;

    if (!$conn) return false;

    try {
        $query = "UPDATE leads SET
                  name = ?,
                  email = ?,
                  phone = ?,
                  company = ?,
                  service = ?,
                  status = ?,
                  source = ?,
                  priority = ?,
                  notes = ?,
                  estimated_value = ?,
                  updated_at = NOW()
                  WHERE id = ?";

        $stmt = $conn->prepare($query);
        return $stmt->execute([
            $lead_data['name'] ?? '',
            $lead_data['email'] ?? '',
            $lead_data['phone'] ?? '',
            $lead_data['company'] ?? '',
            $lead_data['service'] ?? '',
            $lead_data['status'] ?? 'new',
            $lead_data['source'] ?? 'manual',
            $lead_data['priority'] ?? 'medium',
            $lead_data['notes'] ?? '',
            floatval($lead_data['estimated_value'] ?? 0),
            intval($lead_id)
        ]);
    } catch (PDOException $e) {
        error_log("Error updating lead: " . $e->getMessage());
        return false;
    }
}

function deleteLead($lead_id) {
    global $conn;
    
    if (!$conn) return false;
    
    $lead_id = intval($lead_id);
    $query = "DELETE FROM leads WHERE id = $lead_id";
    
    return mysqli_query($conn, $query);
}

function getLead($lead_id) {
    global $conn;
    
    if (!$conn) return false;
    
    $lead_id = intval($lead_id);
    $query = "SELECT * FROM leads WHERE id = $lead_id";
    $result = mysqli_query($conn, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }
    
    return false;
}

// ==================== DASHBOARD STATISTICS FUNCTIONS ====================

function getDashboardStats() {
    global $conn;
    
    if (!$conn) {
        return [
            'total_leads' => 0,
            'new_leads' => 0,
            'hot_leads' => 0,
            'converted_leads' => 0
        ];
    }
    
    $stats = [];
    
    // Total leads
    $query = "SELECT COUNT(*) as count FROM leads";
    $result = mysqli_query($conn, $query);
    $stats['total_leads'] = ($result) ? mysqli_fetch_assoc($result)['count'] : 0;
    
    // New leads
    $query = "SELECT COUNT(*) as count FROM leads WHERE status = 'new'";
    $result = mysqli_query($conn, $query);
    $stats['new_leads'] = ($result) ? mysqli_fetch_assoc($result)['count'] : 0;
    
    // Hot leads
    $query = "SELECT COUNT(*) as count FROM leads WHERE status = 'hot'";
    $result = mysqli_query($conn, $query);
    $stats['hot_leads'] = ($result) ? mysqli_fetch_assoc($result)['count'] : 0;
    
    // Converted leads
    $query = "SELECT COUNT(*) as count FROM leads WHERE status = 'converted'";
    $result = mysqli_query($conn, $query);
    $stats['converted_leads'] = ($result) ? mysqli_fetch_assoc($result)['count'] : 0;
    
    return $stats;
}

// ==================== UTILITY FUNCTIONS ====================

function getServices() {
    global $conn;
    
    if (!$conn) return [];
    
    $query = "SELECT * FROM services WHERE status = 'active' ORDER BY name";
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        return [];
    }
    
    $services = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $services[] = $row;
    }
    
    return $services;
}

function getSetting($key, $default = null) {
    global $conn;
    
    if (!$conn) return $default;
    
    $key = mysqli_real_escape_string($conn, $key);
    $query = "SELECT setting_value FROM settings WHERE setting_key = '$key'";
    $result = mysqli_query($conn, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        return $row['setting_value'];
    }
    
    return $default;
}

function setSetting($key, $value, $type = 'string') {
    global $conn;
    
    if (!$conn) return false;
    
    $key = mysqli_real_escape_string($conn, $key);
    $value = mysqli_real_escape_string($conn, $value);
    $type = mysqli_real_escape_string($conn, $type);
    
    $query = "INSERT INTO settings (setting_key, setting_value, setting_type) 
              VALUES ('$key', '$value', '$type') 
              ON DUPLICATE KEY UPDATE setting_value = '$value', setting_type = '$type'";
    
    return mysqli_query($conn, $query);
}

// Close connection function
function closeConnection() {
    global $conn;
    if ($conn) {
        mysqli_close($conn);
    }
}
?>
