<?php
// CRM Pro - Simple File-Based Storage (No Database Required)
// All data stored in JSON files - Pure PHP procedural approach

// Data storage paths
$data_dir = 'data/';
$leads_file = $data_dir . 'leads.json';
$users_file = $data_dir . 'users.json';
$settings_file = $data_dir . 'settings.json';

// Create data directory if it doesn't exist
if (!file_exists($data_dir)) {
    mkdir($data_dir, 0777, true);
}

// Initialize data files if they don't exist
initializeDataFiles();

// ==================== INITIALIZATION FUNCTIONS ====================

function initializeDataFiles() {
    global $leads_file, $users_file, $settings_file;

    // Initialize leads file
    if (!file_exists($leads_file)) {
        $initial_leads = [];
        file_put_contents($leads_file, json_encode($initial_leads, JSON_PRETTY_PRINT));
    }

    // Initialize users file
    if (!file_exists($users_file)) {
        $initial_users = [
            [
                'id' => 1,
                'username' => 'superadmin',
                'password' => 'super123',
                'role' => 'superadmin',
                'full_name' => 'Super Admin',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'username' => 'Suraj',
                'password' => 'Suraj',
                'role' => 'admin',
                'full_name' => 'Suraj',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 3,
                'username' => 'admin',
                'password' => 'admin123',
                'role' => 'admin',
                'full_name' => 'Admin',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 4,
                'username' => 'user',
                'password' => 'user123',
                'role' => 'user',
                'full_name' => 'User',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
        file_put_contents($users_file, json_encode($initial_users, JSON_PRETTY_PRINT));
    }

    // Initialize settings file
    if (!file_exists($settings_file)) {
        $initial_settings = [
            'company_name' => 'CRM Pro',
            'company_email' => '<EMAIL>',
            'timezone' => 'UTC'
        ];
        file_put_contents($settings_file, json_encode($initial_settings, JSON_PRETTY_PRINT));
    }
}

// ==================== USER AUTHENTICATION FUNCTIONS ====================

function authenticateUser($username, $password) {
    global $users_file;

    // Read users from file
    $users = readJsonFile($users_file);
    if (!$users) return false;

    // Find user by username and password
    foreach ($users as $user) {
        if ($user['username'] === $username && $user['password'] === $password) {
            // Update last login
            $user['last_login'] = date('Y-m-d H:i:s');
            updateUserLastLogin($user['id']);

            return $user;
        }
    }

    return false;
}

function updateUserLastLogin($user_id) {
    global $users_file;

    $users = readJsonFile($users_file);
    if (!$users) return false;

    // Update last login for the user
    for ($i = 0; $i < count($users); $i++) {
        if ($users[$i]['id'] == $user_id) {
            $users[$i]['last_login'] = date('Y-m-d H:i:s');
            break;
        }
    }

    return writeJsonFile($users_file, $users);
}

// ==================== FILE HELPER FUNCTIONS ====================

function readJsonFile($filename) {
    if (!file_exists($filename)) {
        return [];
    }

    $content = file_get_contents($filename);
    if ($content === false) {
        return [];
    }

    $data = json_decode($content, true);
    return $data ? $data : [];
}

function writeJsonFile($filename, $data) {
    $json = json_encode($data, JSON_PRETTY_PRINT);
    return file_put_contents($filename, $json) !== false;
}

function getNextId($data) {
    if (empty($data)) {
        return 1;
    }

    $max_id = 0;
    foreach ($data as $item) {
        if (isset($item['id']) && $item['id'] > $max_id) {
            $max_id = $item['id'];
        }
    }

    return $max_id + 1;
}

// ==================== LEAD MANAGEMENT FUNCTIONS ====================

function getLeads($user_id = null, $role = 'user') {
    global $leads_file;

    $leads = readJsonFile($leads_file);

    // Sort by created_at descending
    usort($leads, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    return $leads;
}

function addLead($lead_data) {
    global $leads_file;

    $leads = readJsonFile($leads_file);

    // Create new lead
    $new_lead = [
        'id' => getNextId($leads),
        'name' => $lead_data['name'] ?? '',
        'email' => $lead_data['email'] ?? '',
        'phone' => $lead_data['phone'] ?? '',
        'company' => $lead_data['company'] ?? '',
        'service' => $lead_data['service'] ?? '',
        'status' => $lead_data['status'] ?? 'new',
        'source' => $lead_data['source'] ?? 'manual',
        'priority' => $lead_data['priority'] ?? 'medium',
        'notes' => $lead_data['notes'] ?? '',
        'estimated_value' => floatval($lead_data['estimated_value'] ?? 0),
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];

    // Add to leads array
    $leads[] = $new_lead;

    // Save to file
    if (writeJsonFile($leads_file, $leads)) {
        return $new_lead['id'];
    }

    return false;
}

function updateLead($lead_id, $lead_data) {
    global $leads_file;

    $leads = readJsonFile($leads_file);

    // Find and update the lead
    for ($i = 0; $i < count($leads); $i++) {
        if ($leads[$i]['id'] == $lead_id) {
            $leads[$i]['name'] = $lead_data['name'] ?? $leads[$i]['name'];
            $leads[$i]['email'] = $lead_data['email'] ?? $leads[$i]['email'];
            $leads[$i]['phone'] = $lead_data['phone'] ?? $leads[$i]['phone'];
            $leads[$i]['company'] = $lead_data['company'] ?? $leads[$i]['company'];
            $leads[$i]['service'] = $lead_data['service'] ?? $leads[$i]['service'];
            $leads[$i]['status'] = $lead_data['status'] ?? $leads[$i]['status'];
            $leads[$i]['source'] = $lead_data['source'] ?? $leads[$i]['source'];
            $leads[$i]['priority'] = $lead_data['priority'] ?? $leads[$i]['priority'];
            $leads[$i]['notes'] = $lead_data['notes'] ?? $leads[$i]['notes'];
            $leads[$i]['estimated_value'] = floatval($lead_data['estimated_value'] ?? $leads[$i]['estimated_value']);
            $leads[$i]['updated_at'] = date('Y-m-d H:i:s');

            return writeJsonFile($leads_file, $leads);
        }
    }

    return false;
}

function deleteLead($lead_id) {
    global $leads_file;

    $leads = readJsonFile($leads_file);

    // Find and remove the lead
    for ($i = 0; $i < count($leads); $i++) {
        if ($leads[$i]['id'] == $lead_id) {
            array_splice($leads, $i, 1);
            return writeJsonFile($leads_file, $leads);
        }
    }

    return false;
}

function getLead($lead_id) {
    global $leads_file;

    $leads = readJsonFile($leads_file);

    // Find the lead
    foreach ($leads as $lead) {
        if ($lead['id'] == $lead_id) {
            return $lead;
        }
    }

    return false;
}

// ==================== DASHBOARD STATISTICS FUNCTIONS ====================

function getDashboardStats() {
    global $leads_file;

    $leads = readJsonFile($leads_file);

    $stats = [
        'total_leads' => 0,
        'new_leads' => 0,
        'hot_leads' => 0,
        'converted_leads' => 0
    ];

    // Count leads by status
    foreach ($leads as $lead) {
        $stats['total_leads']++;

        switch ($lead['status']) {
            case 'new':
                $stats['new_leads']++;
                break;
            case 'hot':
                $stats['hot_leads']++;
                break;
            case 'converted':
                $stats['converted_leads']++;
                break;
        }
    }

    return $stats;
}

// ==================== UTILITY FUNCTIONS ====================

function getServices() {
    // Return default services since we're not using database
    return [
        ['id' => 1, 'name' => 'Web Development', 'status' => 'active'],
        ['id' => 2, 'name' => 'Mobile App Development', 'status' => 'active'],
        ['id' => 3, 'name' => 'Digital Marketing', 'status' => 'active'],
        ['id' => 4, 'name' => 'SEO Services', 'status' => 'active'],
        ['id' => 5, 'name' => 'Consulting', 'status' => 'active']
    ];
}

function getSetting($key, $default = null) {
    global $settings_file;

    $settings = readJsonFile($settings_file);

    return isset($settings[$key]) ? $settings[$key] : $default;
}

function setSetting($key, $value, $type = 'string') {
    global $settings_file;

    $settings = readJsonFile($settings_file);
    $settings[$key] = $value;

    return writeJsonFile($settings_file, $settings);
}

// No connection to close in file-based system
function closeConnection() {
    // Nothing to close in file-based system
    return true;
}
?>
