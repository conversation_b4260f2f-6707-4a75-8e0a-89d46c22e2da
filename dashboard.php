<?php
// Start session
session_start();

// Include database connection
include 'db.php';

// Check if user is logged in
if(!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
    header("Location: login_new.php");
    exit();
}

$role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];
$full_name = $_SESSION['full_name'];

// Get dashboard statistics
$stats = getDashboardStats();
$totalLeads = $stats['total_leads'];
$newLeads = $stats['new_leads'];
$hotLeads = $stats['hot_leads'];
$convertedLeads = $stats['converted_leads'];

// Calculate conversion rate
$conversionRate = $totalLeads > 0 ? round(($convertedLeads / $totalLeads) * 100, 1) : 0;

// Get recent leads
$leads = getLeads($user_id, $role);
$recentLeads = array_slice($leads, 0, 5); // Get first 5 leads
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Pro - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Arial', sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .main-content {
            padding: 2rem 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            margin-bottom: 1.5rem;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .bg-primary-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .bg-success-gradient { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .bg-warning-gradient { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .bg-info-gradient { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
        
        .table-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table th {
            border: none;
            font-weight: 600;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .table td {
            border: none;
            vertical-align: middle;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        
        .btn-action {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>CRM Pro
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($full_name); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="leads.php"><i class="fas fa-users me-2"></i>Manage Leads</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <!-- Welcome Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>Welcome back, <?php echo htmlspecialchars($full_name); ?>!</h2>
                    <p class="text-muted">Here's what's happening with your CRM today.</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary-gradient">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number"><?php echo $totalLeads; ?></div>
                        <div class="stat-label">Total Leads</div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-info-gradient">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-number"><?php echo $newLeads; ?></div>
                        <div class="stat-label">New Leads</div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning-gradient">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="stat-number"><?php echo $hotLeads; ?></div>
                        <div class="stat-label">Hot Leads</div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-success-gradient">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number"><?php echo $convertedLeads; ?></div>
                        <div class="stat-label">Converted</div>
                    </div>
                </div>
            </div>

            <!-- Conversion Rate -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="stat-card">
                        <h5>Conversion Rate</h5>
                        <div class="stat-number text-success"><?php echo $conversionRate; ?>%</div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" style="width: <?php echo $conversionRate; ?>%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="stat-card">
                        <h5>Quick Actions</h5>
                        <div class="d-grid gap-2">
                            <a href="leads.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add New Lead
                            </a>
                            <a href="leads.php" class="btn btn-outline-primary">
                                <i class="fas fa-list me-2"></i>View All Leads
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Leads Table -->
            <div class="row">
                <div class="col-12">
                    <div class="table-card">
                        <h5 class="mb-3">Recent Leads</h5>
                        
                        <?php if(empty($recentLeads)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No leads found</h6>
                                <p class="text-muted">Start by adding your first lead!</p>
                                <a href="leads.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add Lead
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Company</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($recentLeads as $lead): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($lead['name']); ?></td>
                                            <td><?php echo htmlspecialchars($lead['email']); ?></td>
                                            <td><?php echo htmlspecialchars($lead['company']); ?></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                switch($lead['status']) {
                                                    case 'new': $statusClass = 'bg-info'; break;
                                                    case 'hot': $statusClass = 'bg-warning'; break;
                                                    case 'converted': $statusClass = 'bg-success'; break;
                                                    default: $statusClass = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $statusClass; ?>">
                                                    <?php echo ucfirst($lead['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M j, Y', strtotime($lead['created_at'])); ?></td>
                                            <td>
                                                <a href="leads.php?edit=<?php echo $lead['id']; ?>" class="btn btn-sm btn-outline-primary btn-action">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="leads.php" class="btn btn-outline-primary">
                                    View All Leads <i class="fas fa-arrow-right ms-2"></i>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
