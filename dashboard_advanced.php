<?php
session_start();
include 'db.php';

// Check if user is logged in and has a role
if(!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
    header("Location: login.php");
    exit();
}

$role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];

// Get leads based on user role
$leads = getLeads($user_id, $role);

// Get dashboard statistics from MySQL
$stats = getDashboardStats();
$totalLeads = $stats['total_leads'];
$newLeads = $stats['new_leads'];
$hotLeads = $stats['hot_leads'];
$convertedLeads = $stats['converted_leads'];

// Conversion rate
$conversionRate = $totalLeads > 0 ? round(($convertedLeads / $totalLeads) * 100, 1) : 0;

// Monthly data for advanced charts from MySQL
$monthlyData = [];
$revenueData = [];

// Get monthly data from database
if (isset($stats['monthly_data'])) {
    foreach ($stats['monthly_data'] as $month => $count) {
        $monthName = date('M Y', strtotime($month . '-01'));
        $monthlyData[$monthName] = $count;
        $revenueData[$monthName] = $count * 2500; // Estimated revenue
    }
} else {
    // Fallback data if no database data
    for($i = 11; $i >= 0; $i--) {
        $monthName = date('M Y', strtotime("-$i months"));
        $monthlyData[$monthName] = 0;
        $revenueData[$monthName] = 0;
    }
}

// Lead sources data
$sourceData = [];
$sources = ['website', 'social-media', 'referral', 'advertisement', 'manual'];
foreach($sources as $source) {
    $sourceData[$source] = count(array_filter($leads, function($lead) use ($source) {
        return ($lead['source'] ?? 'manual') === $source;
    }));
}

// Recent activities (mock data for demo)
$recentActivities = [
    ['type' => 'lead_added', 'user' => 'John Admin', 'description' => 'Added new lead: Sarah Johnson', 'time' => '2 minutes ago', 'icon' => 'user-plus', 'color' => 'success'],
    ['type' => 'status_changed', 'user' => 'Jane User', 'description' => 'Changed lead status to Hot', 'time' => '15 minutes ago', 'icon' => 'fire', 'color' => 'warning'],
    ['type' => 'lead_converted', 'user' => 'Mike Admin', 'description' => 'Converted lead to customer', 'time' => '1 hour ago', 'icon' => 'check-circle', 'color' => 'success'],
    ['type' => 'email_sent', 'user' => 'System', 'description' => 'Follow-up email sent to 5 leads', 'time' => '2 hours ago', 'icon' => 'envelope', 'color' => 'info'],
    ['type' => 'report_generated', 'user' => 'Admin', 'description' => 'Monthly report generated', 'time' => '3 hours ago', 'icon' => 'chart-bar', 'color' => 'primary']
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Pro - Advanced Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #f7fafc;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --success-color: #48bb78;
            --danger-color: #f56565;
            --warning-color: #ed8936;
            --info-color: #4299e1;
            --sidebar-width: 280px;
            
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: var(--text-primary);
            overflow-x: hidden;
        }
        
        /* Advanced Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(180deg, #1a202c 0%, #2d3748 100%);
            color: white;
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
        }
        
        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .sidebar-brand {
            font-size: 1.75rem;
            font-weight: 800;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .sidebar-brand:hover {
            color: #667eea;
            transform: scale(1.05);
        }
        
        .sidebar-brand i {
            margin-right: 0.75rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-section {
            margin-bottom: 2rem;
        }
        
        .nav-section-title {
            padding: 0 1.5rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.5);
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
            position: relative;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            border-radius: 0;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(8px);
            backdrop-filter: blur(10px);
        }
        
        .nav-link:hover::before,
        .nav-link.active::before {
            transform: scaleY(1);
        }
        
        .nav-link i {
            width: 24px;
            margin-right: 1rem;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover i {
            transform: scale(1.2);
        }
        
        .nav-badge {
            margin-left: auto;
            background: var(--danger-gradient);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-weight: 600;
        }
        
        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        /* Advanced Top Navbar */
        .top-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1.5rem 2rem;
            box-shadow: var(--shadow-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
            border-bottom: 1px solid var(--border-color);
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .search-box {
            position: relative;
            width: 300px;
        }
        
        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            background: white;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: scale(1.02);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.25rem;
            padding: 0.75rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .notification-btn:hover {
            background: var(--secondary-color);
            color: var(--primary-color);
            transform: scale(1.1);
        }
        
        .notification-badge {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            background: var(--danger-color);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 50px;
            min-width: 1.2rem;
            text-align: center;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .user-info:hover {
            background: var(--secondary-color);
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }
        
        .user-avatar:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }
        
        /* Content Area */
        .content-area {
            padding: 2rem;
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Advanced Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--gradient);
            transition: height 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-xl);
        }
        
        .stat-card:hover::before {
            height: 8px;
        }
        
        .stat-card.primary::before { background: var(--primary-gradient); }
        .stat-card.success::before { background: var(--success-gradient); }
        .stat-card.warning::before { background: var(--warning-gradient); }
        .stat-card.info::before { background: var(--info-gradient); }
        .stat-card.danger::before { background: var(--danger-gradient); }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
        }
        
        .stat-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: white;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }
        
        .stat-icon.primary { background: var(--primary-gradient); }
        .stat-icon.success { background: var(--success-gradient); }
        .stat-icon.warning { background: var(--warning-gradient); }
        .stat-icon.info { background: var(--info-gradient); }
        .stat-icon.danger { background: var(--danger-gradient); }
        
        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        .stat-value {
            font-size: 3rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-change {
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }
        
        .stat-change.positive { color: var(--success-color); }
        .stat-change.negative { color: var(--danger-color); }
        
        .stat-trend {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }
        
        /* Advanced Chart Container */
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
        }
        
        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .chart-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }
        
        .chart-subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }
        
        .chart-controls {
            display: flex;
            gap: 0.5rem;
        }
        
        .chart-btn {
            padding: 0.5rem 1rem;
            border: 2px solid var(--border-color);
            background: white;
            color: var(--text-secondary);
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .chart-btn.active,
        .chart-btn:hover {
            background: var(--primary-gradient);
            border-color: transparent;
            color: white;
            transform: scale(1.05);
        }
        
        /* Activity Feed */
        .activity-feed {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            height: fit-content;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-item:hover {
            background: var(--secondary-color);
            margin: 0 -1rem;
            padding: 1rem;
            border-radius: 12px;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            color: white;
            flex-shrink: 0;
        }
        
        .activity-icon.success { background: var(--success-gradient); }
        .activity-icon.warning { background: var(--warning-gradient); }
        .activity-icon.info { background: var(--info-gradient); }
        .activity-icon.primary { background: var(--primary-gradient); }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-description {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .activity-meta {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }
        
        /* Quick Actions */
        .quick-actions {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            padding: 2rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 16px;
            background: white;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
        }
        
        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            transition: left 0.3s ease;
            z-index: -1;
        }
        
        .action-btn:hover {
            color: white;
            border-color: transparent;
            transform: translateY(-5px) scale(1.05);
            box-shadow: var(--shadow-xl);
        }
        
        .action-btn:hover::before {
            left: 0;
        }
        
        .action-icon {
            font-size: 2rem;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover .action-icon {
            transform: scale(1.2);
        }
        
        .action-title {
            font-weight: 600;
            font-size: 1rem;
        }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .top-navbar {
                padding: 1rem;
            }
            
            .content-area {
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .search-box {
                width: 200px;
            }
            
            .page-title {
                font-size: 1.5rem;
            }
        }
        
        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Pulse Animation */
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        /* Floating Animation */
        .float {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Advanced Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard_advanced.php" class="sidebar-brand">
                <i class="fas fa-rocket"></i>CRM Pro
            </a>
        </div>
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="dashboard_advanced.php" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                        <span class="nav-badge">New</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="leads_advanced.php" class="nav-link">
                        <i class="fas fa-users"></i>Leads
                        <span class="nav-badge"><?php echo $totalLeads; ?></span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="pipeline.php" class="nav-link">
                        <i class="fas fa-chart-line"></i>Sales Pipeline
                    </a>
                </div>
                <div class="nav-item">
                    <a href="contacts.php" class="nav-link">
                        <i class="fas fa-address-book"></i>Contacts
                    </a>
                </div>
            </div>

            <?php if($role == 'admin' || $role == 'superadmin'): ?>
            <div class="nav-section">
                <div class="nav-section-title">Analytics</div>
                <div class="nav-item">
                    <a href="analytics_advanced.php" class="nav-link">
                        <i class="fas fa-chart-bar"></i>Analytics
                    </a>
                </div>
                <div class="nav-item">
                    <a href="reports_advanced.php" class="nav-link">
                        <i class="fas fa-file-alt"></i>Reports
                    </a>
                </div>
                <div class="nav-item">
                    <a href="forecasting.php" class="nav-link">
                        <i class="fas fa-crystal-ball"></i>Forecasting
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <div class="nav-section">
                <div class="nav-section-title">Tools</div>
                <div class="nav-item">
                    <a href="email_campaigns.php" class="nav-link">
                        <i class="fas fa-envelope-open-text"></i>Email Campaigns
                    </a>
                </div>
                <div class="nav-item">
                    <a href="automation.php" class="nav-link">
                        <i class="fas fa-robot"></i>Automation
                    </a>
                </div>
                <div class="nav-item">
                    <a href="integrations.php" class="nav-link">
                        <i class="fas fa-plug"></i>Integrations
                    </a>
                </div>
            </div>

            <?php if($role == 'superadmin'): ?>
            <div class="nav-section">
                <div class="nav-section-title">Admin</div>
                <div class="nav-item">
                    <a href="users_advanced.php" class="nav-link">
                        <i class="fas fa-user-cog"></i>User Management
                    </a>
                </div>
                <div class="nav-item">
                    <a href="settings_advanced.php" class="nav-link">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                </div>
                <div class="nav-item">
                    <a href="security.php" class="nav-link">
                        <i class="fas fa-shield-alt"></i>Security
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <div class="nav-section">
                <div class="nav-section-title">Account</div>
                <div class="nav-item">
                    <a href="profile_advanced.php" class="nav-link">
                        <i class="fas fa-user"></i>Profile
                    </a>
                </div>
                <div class="nav-item">
                    <a href="notifications.php" class="nav-link">
                        <i class="fas fa-bell"></i>Notifications
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Advanced Top Navbar -->
        <div class="top-navbar">
            <div>
                <h1 class="page-title">Dashboard</h1>
                <div class="chart-subtitle">Welcome back, <?php echo ucfirst($_SESSION['username']); ?>! Here's what's happening today.</div>
            </div>

            <div class="navbar-actions">
                <div class="search-box">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search leads, contacts, deals...">
                </div>

                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>

                <div class="user-menu">
                    <div class="user-info">
                        <div class="user-avatar pulse">
                            <?php echo strtoupper(substr($_SESSION['username'], 0, 1)); ?>
                        </div>
                        <div>
                            <div class="fw-semibold"><?php echo ucfirst($_SESSION['username']); ?></div>
                            <small class="text-muted"><?php echo ucfirst($_SESSION['role']); ?></small>
                        </div>
                    </div>
                    <a href="logout.php" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </a>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Advanced Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card primary animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Total Leads</div>
                            <div class="stat-value"><?php echo $totalLeads; ?></div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>12% from last month</span>
                            </div>
                            <div class="stat-trend">Trending upward this quarter</div>
                        </div>
                        <div class="stat-icon primary float">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card success animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">New This Week</div>
                            <div class="stat-value"><?php echo $newLeads; ?></div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>8% from last week</span>
                            </div>
                            <div class="stat-trend">Strong weekly performance</div>
                        </div>
                        <div class="stat-icon success float">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card warning animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Hot Leads</div>
                            <div class="stat-value"><?php echo $hotLeads; ?></div>
                            <div class="stat-change positive">
                                <i class="fas fa-fire"></i>
                                <span><?php echo $conversionRate; ?>% conversion rate</span>
                            </div>
                            <div class="stat-trend">High-priority prospects</div>
                        </div>
                        <div class="stat-icon warning float">
                            <i class="fas fa-fire"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card info animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Converted</div>
                            <div class="stat-value"><?php echo $convertedLeads; ?></div>
                            <div class="stat-change positive">
                                <i class="fas fa-check-circle"></i>
                                <span>5% this month</span>
                            </div>
                            <div class="stat-trend">Revenue generating</div>
                        </div>
                        <div class="stat-icon info float">
                            <i class="fas fa-trophy"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Charts Section -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="chart-container animate__animated animate__fadeInUp" style="animation-delay: 0.5s;">
                        <div class="chart-header">
                            <div>
                                <h3 class="chart-title">Lead Generation Trend</h3>
                                <div class="chart-subtitle">Monthly lead acquisition over the past year</div>
                            </div>
                            <div class="chart-controls">
                                <button class="chart-btn active" data-period="12m">12M</button>
                                <button class="chart-btn" data-period="6m">6M</button>
                                <button class="chart-btn" data-period="3m">3M</button>
                                <button class="chart-btn" data-period="1m">1M</button>
                            </div>
                        </div>
                        <canvas id="leadsChart" height="100"></canvas>
                    </div>

                    <div class="chart-container animate__animated animate__fadeInUp" style="animation-delay: 0.6s;">
                        <div class="chart-header">
                            <div>
                                <h3 class="chart-title">Revenue Forecast</h3>
                                <div class="chart-subtitle">Projected revenue based on current pipeline</div>
                            </div>
                        </div>
                        <canvas id="revenueChart" height="80"></canvas>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="chart-container animate__animated animate__fadeInUp" style="animation-delay: 0.7s;">
                        <div class="chart-header">
                            <div>
                                <h3 class="chart-title">Lead Sources</h3>
                                <div class="chart-subtitle">Where your leads come from</div>
                            </div>
                        </div>
                        <canvas id="sourceChart" height="200"></canvas>
                    </div>

                    <div class="activity-feed animate__animated animate__fadeInUp" style="animation-delay: 0.8s;">
                        <div class="chart-header">
                            <h3 class="chart-title">Recent Activity</h3>
                        </div>
                        <?php foreach($recentActivities as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-icon <?php echo $activity['color']; ?>">
                                <i class="fas fa-<?php echo $activity['icon']; ?>"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-description"><?php echo $activity['description']; ?></div>
                                <div class="activity-meta">
                                    <span><?php echo $activity['user']; ?></span> •
                                    <span><?php echo $activity['time']; ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <div class="text-center mt-3">
                            <a href="activity_log.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-history me-1"></i>View All Activity
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions animate__animated animate__fadeInUp" style="animation-delay: 0.9s;">
                <div class="chart-header">
                    <h3 class="chart-title">Quick Actions</h3>
                    <div class="chart-subtitle">Frequently used features for faster workflow</div>
                </div>
                <div class="action-grid">
                    <a href="leads_advanced.php?action=add" class="action-btn">
                        <div class="action-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="action-title">Add New Lead</div>
                    </a>

                    <a href="leads_advanced.php" class="action-btn">
                        <div class="action-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="action-title">View All Leads</div>
                    </a>

                    <a href="export_advanced.php" class="action-btn">
                        <div class="action-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="action-title">Export Data</div>
                    </a>

                    <a href="reports_advanced.php" class="action-btn">
                        <div class="action-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="action-title">Generate Report</div>
                    </a>

                    <a href="email_campaigns.php" class="action-btn">
                        <div class="action-icon">
                            <i class="fas fa-envelope-open-text"></i>
                        </div>
                        <div class="action-title">Email Campaign</div>
                    </a>

                    <a href="automation.php" class="action-btn">
                        <div class="action-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="action-title">Setup Automation</div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Advanced Chart Configurations
        Chart.defaults.font.family = 'Inter';
        Chart.defaults.color = '#718096';

        // Leads Trend Chart
        const ctx = document.getElementById('leadsChart').getContext('2d');
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(102, 126, 234, 0.3)');
        gradient.addColorStop(1, 'rgba(102, 126, 234, 0.05)');

        const leadsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_keys($monthlyData)); ?>,
                datasets: [{
                    label: 'Leads',
                    data: <?php echo json_encode(array_values($monthlyData)); ?>,
                    borderColor: '#667eea',
                    backgroundColor: gradient,
                    borderWidth: 4,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 3,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f1f5f9',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            padding: 10
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#667eea'
                    }
                }
            }
        });

        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueGradient = revenueCtx.createLinearGradient(0, 0, 0, 300);
        revenueGradient.addColorStop(0, 'rgba(72, 187, 120, 0.3)');
        revenueGradient.addColorStop(1, 'rgba(72, 187, 120, 0.05)');

        const revenueChart = new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode(array_keys($revenueData)); ?>,
                datasets: [{
                    label: 'Revenue',
                    data: <?php echo json_encode(array_values($revenueData)); ?>,
                    backgroundColor: revenueGradient,
                    borderColor: '#48bb78',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#48bb78',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return 'Revenue: $' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f1f5f9',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            padding: 10
                        }
                    }
                }
            }
        });

        // Source Chart (Doughnut)
        const sourceCtx = document.getElementById('sourceChart').getContext('2d');
        const sourceChart = new Chart(sourceCtx, {
            type: 'doughnut',
            data: {
                labels: ['Website', 'Social Media', 'Referral', 'Advertisement', 'Manual'],
                datasets: [{
                    data: <?php echo json_encode(array_values($sourceData)); ?>,
                    backgroundColor: [
                        '#667eea',
                        '#f093fb',
                        '#4facfe',
                        '#43e97b',
                        '#fa709a'
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        cornerRadius: 8,
                        displayColors: true
                    }
                }
            }
        });

        // Chart Controls
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Here you would typically fetch new data based on the period
                // For demo purposes, we'll just show a loading state
                const spinner = document.createElement('div');
                spinner.className = 'loading-spinner';
                this.appendChild(spinner);

                setTimeout(() => {
                    spinner.remove();
                }, 1000);
            });
        });

        // Search functionality
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            // Implement search logic here
            console.log('Searching for:', query);
        });

        // Notification click
        document.querySelector('.notification-btn').addEventListener('click', function() {
            // Show notifications dropdown
            console.log('Show notifications');
        });

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            // Refresh dashboard data
            console.log('Refreshing dashboard data...');
        }, 30000);

        // Add smooth scrolling to all anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
