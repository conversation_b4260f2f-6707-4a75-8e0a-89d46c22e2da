<?php
// Start session
session_start();

// Include database connection
include 'db.php';

// Check if user is logged in
if(!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
    header("Location: login_new.php");
    exit();
}

$role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];
$full_name = $_SESSION['full_name'];

$message = '';
$error = '';

// Handle form submissions
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['action'])) {
        switch($_POST['action']) {
            case 'add':
                $lead_data = [
                    'name' => $_POST['name'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'phone' => $_POST['phone'] ?? '',
                    'company' => $_POST['company'] ?? '',
                    'service' => $_POST['service'] ?? '',
                    'status' => $_POST['status'] ?? 'new',
                    'source' => $_POST['source'] ?? 'manual',
                    'priority' => $_POST['priority'] ?? 'medium',
                    'notes' => $_POST['notes'] ?? '',
                    'estimated_value' => $_POST['estimated_value'] ?? 0
                ];
                
                if(addLead($lead_data)) {
                    $message = 'Lead added successfully!';
                } else {
                    $error = 'Failed to add lead.';
                }
                break;
                
            case 'edit':
                $lead_id = $_POST['lead_id'] ?? 0;
                $lead_data = [
                    'name' => $_POST['name'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'phone' => $_POST['phone'] ?? '',
                    'company' => $_POST['company'] ?? '',
                    'service' => $_POST['service'] ?? '',
                    'status' => $_POST['status'] ?? 'new',
                    'source' => $_POST['source'] ?? 'manual',
                    'priority' => $_POST['priority'] ?? 'medium',
                    'notes' => $_POST['notes'] ?? '',
                    'estimated_value' => $_POST['estimated_value'] ?? 0
                ];
                
                if(updateLead($lead_id, $lead_data)) {
                    $message = 'Lead updated successfully!';
                } else {
                    $error = 'Failed to update lead.';
                }
                break;
                
            case 'delete':
                $lead_id = $_POST['lead_id'] ?? 0;
                if(deleteLead($lead_id)) {
                    $message = 'Lead deleted successfully!';
                } else {
                    $error = 'Failed to delete lead.';
                }
                break;
        }
    }
}

// Get all leads
$leads = getLeads($user_id, $role);

// Get lead for editing if edit parameter is set
$editLead = null;
if(isset($_GET['edit'])) {
    $editLead = getLead($_GET['edit']);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Pro - Leads Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Arial', sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .main-content {
            padding: 2rem 0;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        
        .table th {
            border: none;
            font-weight: 600;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .table td {
            border: none;
            vertical-align: middle;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        
        .btn-action {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
            margin: 0 2px;
        }
        
        .form-control, .form-select {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 0.75rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-chart-line me-2"></i>CRM Pro
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($full_name); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>Leads Management</h2>
                    <p class="text-muted">Manage your leads and track their progress.</p>
                </div>
            </div>

            <!-- Messages -->
            <?php if($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Add/Edit Lead Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-<?php echo $editLead ? 'edit' : 'plus'; ?> me-2"></i>
                        <?php echo $editLead ? 'Edit Lead' : 'Add New Lead'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="<?php echo $editLead ? 'edit' : 'add'; ?>">
                        <?php if($editLead): ?>
                            <input type="hidden" name="lead_id" value="<?php echo $editLead['id']; ?>">
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Name *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo $editLead ? htmlspecialchars($editLead['name']) : ''; ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo $editLead ? htmlspecialchars($editLead['email']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="text" class="form-control" id="phone" name="phone" 
                                       value="<?php echo $editLead ? htmlspecialchars($editLead['phone']) : ''; ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" class="form-control" id="company" name="company" 
                                       value="<?php echo $editLead ? htmlspecialchars($editLead['company']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="new" <?php echo ($editLead && $editLead['status'] == 'new') ? 'selected' : ''; ?>>New</option>
                                    <option value="contacted" <?php echo ($editLead && $editLead['status'] == 'contacted') ? 'selected' : ''; ?>>Contacted</option>
                                    <option value="qualified" <?php echo ($editLead && $editLead['status'] == 'qualified') ? 'selected' : ''; ?>>Qualified</option>
                                    <option value="hot" <?php echo ($editLead && $editLead['status'] == 'hot') ? 'selected' : ''; ?>>Hot</option>
                                    <option value="converted" <?php echo ($editLead && $editLead['status'] == 'converted') ? 'selected' : ''; ?>>Converted</option>
                                    <option value="lost" <?php echo ($editLead && $editLead['status'] == 'lost') ? 'selected' : ''; ?>>Lost</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" <?php echo ($editLead && $editLead['priority'] == 'low') ? 'selected' : ''; ?>>Low</option>
                                    <option value="medium" <?php echo ($editLead && $editLead['priority'] == 'medium') ? 'selected' : ''; ?>>Medium</option>
                                    <option value="high" <?php echo ($editLead && $editLead['priority'] == 'high') ? 'selected' : ''; ?>>High</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="source" class="form-label">Source</label>
                                <select class="form-select" id="source" name="source">
                                    <option value="website" <?php echo ($editLead && $editLead['source'] == 'website') ? 'selected' : ''; ?>>Website</option>
                                    <option value="social-media" <?php echo ($editLead && $editLead['source'] == 'social-media') ? 'selected' : ''; ?>>Social Media</option>
                                    <option value="referral" <?php echo ($editLead && $editLead['source'] == 'referral') ? 'selected' : ''; ?>>Referral</option>
                                    <option value="advertisement" <?php echo ($editLead && $editLead['source'] == 'advertisement') ? 'selected' : ''; ?>>Advertisement</option>
                                    <option value="manual" <?php echo ($editLead && $editLead['source'] == 'manual') ? 'selected' : ''; ?>>Manual</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="service" class="form-label">Service</label>
                                <input type="text" class="form-control" id="service" name="service" 
                                       value="<?php echo $editLead ? htmlspecialchars($editLead['service']) : ''; ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="estimated_value" class="form-label">Estimated Value</label>
                                <input type="number" class="form-control" id="estimated_value" name="estimated_value" 
                                       step="0.01" value="<?php echo $editLead ? $editLead['estimated_value'] : '0'; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $editLead ? htmlspecialchars($editLead['notes']) : ''; ?></textarea>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo $editLead ? 'Update Lead' : 'Add Lead'; ?>
                            </button>
                            
                            <?php if($editLead): ?>
                                <a href="leads.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Leads Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>All Leads
                    </h5>
                </div>
                <div class="card-body">
                    <?php if(empty($leads)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No leads found</h6>
                            <p class="text-muted">Start by adding your first lead using the form above!</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Company</th>
                                        <th>Status</th>
                                        <th>Priority</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($leads as $lead): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($lead['name']); ?></td>
                                        <td><?php echo htmlspecialchars($lead['email']); ?></td>
                                        <td><?php echo htmlspecialchars($lead['phone']); ?></td>
                                        <td><?php echo htmlspecialchars($lead['company']); ?></td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            switch($lead['status']) {
                                                case 'new': $statusClass = 'bg-info'; break;
                                                case 'contacted': $statusClass = 'bg-primary'; break;
                                                case 'qualified': $statusClass = 'bg-warning'; break;
                                                case 'hot': $statusClass = 'bg-danger'; break;
                                                case 'converted': $statusClass = 'bg-success'; break;
                                                case 'lost': $statusClass = 'bg-secondary'; break;
                                                default: $statusClass = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $statusClass; ?>">
                                                <?php echo ucfirst($lead['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $priorityClass = '';
                                            switch($lead['priority']) {
                                                case 'high': $priorityClass = 'bg-danger'; break;
                                                case 'medium': $priorityClass = 'bg-warning'; break;
                                                case 'low': $priorityClass = 'bg-success'; break;
                                                default: $priorityClass = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $priorityClass; ?>">
                                                <?php echo ucfirst($lead['priority']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($lead['created_at'])); ?></td>
                                        <td>
                                            <a href="leads.php?edit=<?php echo $lead['id']; ?>" class="btn btn-sm btn-outline-primary btn-action">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this lead?');">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="lead_id" value="<?php echo $lead['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-danger btn-action">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
