<?php
session_start();
include 'db.php';

// Check if user is logged in
if(!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
    header("Location: login.php");
    exit();
}

$role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];

// Get leads based on user role
$leads = getLeads($user_id, $role);

// Handle lead actions
if($_POST) {
    if(isset($_POST['action'])) {
        switch($_POST['action']) {
            case 'add':
                $lead = [
                    'name' => $_POST['name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'company' => $_POST['company'] ?? '',
                    'service' => $_POST['service'] ?? '',
                    'status' => $_POST['status'] ?? 'new',
                    'source' => $_POST['source'] ?? 'manual',
                    'assigned_to' => $_SESSION['user_id'],
                    'created_by' => $_SESSION['user_id'],
                    'notes' => $_POST['notes'] ?? '',
                    'priority' => $_POST['priority'] ?? 'medium',
                    'estimated_value' => $_POST['estimated_value'] ?? 0
                ];
                addLead($lead);
                header("Location: leads_advanced.php?success=added");
                exit();
                break;
            
            case 'update':
                $updates = [
                    'name' => $_POST['name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'company' => $_POST['company'] ?? '',
                    'service' => $_POST['service'] ?? '',
                    'status' => $_POST['status'] ?? 'new',
                    'notes' => $_POST['notes'] ?? '',
                    'priority' => $_POST['priority'] ?? 'medium',
                    'estimated_value' => $_POST['estimated_value'] ?? 0
                ];
                updateLead($_POST['lead_id'], $updates);
                header("Location: leads_advanced.php?success=updated");
                exit();
                break;
            
            case 'delete':
                deleteLead($_POST['lead_id']);
                header("Location: leads_advanced.php?success=deleted");
                exit();
                break;
                
            case 'bulk_update':
                $leadIds = $_POST['lead_ids'] ?? [];
                $bulkStatus = $_POST['bulk_status'] ?? '';
                foreach($leadIds as $id) {
                    updateLead($id, ['status' => $bulkStatus]);
                }
                header("Location: leads_advanced.php?success=bulk_updated");
                exit();
                break;
        }
    }
}

// Filter leads based on user role
if($role == 'user') {
    $leads = array_filter($leads, function($lead) {
        return isset($lead['user_id']) && $lead['user_id'] == $_SESSION['user_id'];
    });
}

// Calculate statistics
$totalLeads = count($leads);
$statusCounts = [];
$priorityCounts = [];
$sourceCounts = [];

foreach(['new', 'contacted', 'qualified', 'hot', 'converted', 'lost'] as $status) {
    $statusCounts[$status] = count(array_filter($leads, function($lead) use ($status) {
        return ($lead['status'] ?? 'new') === $status;
    }));
}

foreach(['low', 'medium', 'high'] as $priority) {
    $priorityCounts[$priority] = count(array_filter($leads, function($lead) use ($priority) {
        return ($lead['priority'] ?? 'medium') === $priority;
    }));
}

foreach(['website', 'social-media', 'referral', 'advertisement', 'manual'] as $source) {
    $sourceCounts[$source] = count(array_filter($leads, function($lead) use ($source) {
        return ($lead['source'] ?? 'manual') === $source;
    }));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Pro - Advanced Lead Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/select/1.7.0/css/select.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            
            --primary-color: #667eea;
            --success-color: #4facfe;
            --warning-color: #43e97b;
            --danger-color: #fa709a;
            --info-color: #a8edea;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --sidebar-width: 280px;
            
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: var(--text-primary);
            overflow-x: hidden;
        }
        
        /* Advanced Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(180deg, #1a202c 0%, #2d3748 100%);
            color: white;
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
        }
        
        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .sidebar-brand {
            font-size: 1.75rem;
            font-weight: 800;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .sidebar-brand:hover {
            color: #667eea;
            transform: scale(1.05);
        }
        
        .sidebar-brand i {
            margin-right: 0.75rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
            position: relative;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            border-radius: 0;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(8px);
            backdrop-filter: blur(10px);
        }
        
        .nav-link:hover::before,
        .nav-link.active::before {
            transform: scaleY(1);
        }
        
        .nav-link i {
            width: 24px;
            margin-right: 1rem;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover i {
            transform: scale(1.2);
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        /* Advanced Top Navbar */
        .top-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1.5rem 2rem;
            box-shadow: var(--shadow-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
            border-bottom: 1px solid var(--border-color);
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .search-box {
            position: relative;
            width: 350px;
        }
        
        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            background: white;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: scale(1.02);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }
        
        .user-avatar:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }
        
        .content-area {
            padding: 2rem;
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Advanced Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient);
            transition: height 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-xl);
        }
        
        .stat-card:hover::before {
            height: 6px;
        }
        
        .stat-card.primary::before { background: var(--primary-gradient); }
        .stat-card.success::before { background: var(--success-gradient); }
        .stat-card.warning::before { background: var(--warning-gradient); }
        .stat-card.danger::before { background: var(--danger-gradient); }
        .stat-card.info::before { background: var(--info-gradient); }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }
        
        .stat-icon.primary { background: var(--primary-gradient); }
        .stat-icon.success { background: var(--success-gradient); }
        .stat-icon.warning { background: var(--warning-gradient); }
        .stat-icon.danger { background: var(--danger-gradient); }
        .stat-icon.info { background: var(--info-gradient); }
        
        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
            line-height: 1;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
        
        /* Advanced Table Container */
        .table-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .table-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .table-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }
        
        .table-actions {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }
        
        .btn-modern {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-modern:hover::before {
            left: 100%;
        }
        
        .btn-primary-modern {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-success-modern {
            background: var(--success-gradient);
            color: white;
        }
        
        .btn-warning-modern {
            background: var(--warning-gradient);
            color: white;
        }
        
        .btn-danger-modern {
            background: var(--danger-gradient);
            color: white;
        }
        
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        /* Advanced DataTable Styling */
        .table {
            margin-bottom: 0;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: none;
            font-weight: 700;
            color: var(--text-primary);
            padding: 1.25rem 1rem;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.5px;
        }
        
        .table tbody td {
            border: none;
            padding: 1.25rem 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
        }
        
        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transform: scale(1.01);
            box-shadow: var(--shadow-md);
        }
        
        /* Status Badges */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .status-badge:hover {
            transform: scale(1.05);
        }
        
        .status-new { 
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(102, 126, 234, 0.2) 100%); 
            color: var(--primary-color); 
            border: 1px solid rgba(102, 126, 234, 0.3);
        }
        .status-contacted { 
            background: linear-gradient(135deg, rgba(168, 237, 234, 0.1) 0%, rgba(168, 237, 234, 0.2) 100%); 
            color: var(--info-color); 
            border: 1px solid rgba(168, 237, 234, 0.3);
        }
        .status-qualified { 
            background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(67, 233, 123, 0.2) 100%); 
            color: var(--warning-color); 
            border: 1px solid rgba(67, 233, 123, 0.3);
        }
        .status-hot { 
            background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(250, 112, 154, 0.2) 100%); 
            color: var(--danger-color); 
            border: 1px solid rgba(250, 112, 154, 0.3);
        }
        .status-converted { 
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(79, 172, 254, 0.2) 100%); 
            color: var(--success-color); 
            border: 1px solid rgba(79, 172, 254, 0.3);
        }
        .status-lost { 
            background: linear-gradient(135deg, rgba(113, 128, 150, 0.1) 0%, rgba(113, 128, 150, 0.2) 100%); 
            color: var(--text-secondary); 
            border: 1px solid rgba(113, 128, 150, 0.3);
        }
        
        /* Priority Badges */
        .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .priority-low { background: rgba(67, 233, 123, 0.2); color: #059669; }
        .priority-medium { background: rgba(250, 112, 154, 0.2); color: #d97706; }
        .priority-high { background: rgba(239, 68, 68, 0.2); color: #dc2626; }
        
        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-action {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-action::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(0);
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        .btn-action:hover::before {
            transform: scale(1);
        }
        
        .btn-action:hover {
            transform: scale(1.1);
        }
        
        .btn-edit {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-delete {
            background: var(--danger-gradient);
            color: white;
        }
        
        .btn-view {
            background: var(--info-gradient);
            color: white;
        }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .top-navbar {
                padding: 1rem;
            }
            
            .content-area {
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .search-box {
                width: 250px;
            }
            
            .page-title {
                font-size: 1.5rem;
            }
            
            .table-actions {
                width: 100%;
                justify-content: center;
            }
        }
        
        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Pulse Animation */
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        /* Floating Animation */
        .float {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Advanced Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard_advanced.php" class="sidebar-brand">
                <i class="fas fa-rocket"></i>CRM Pro
            </a>
        </div>
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard_advanced.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="leads_advanced.php" class="nav-link active">
                    <i class="fas fa-users"></i>Leads
                </a>
            </div>
            <?php if($role == 'admin' || $role == 'superadmin'): ?>
            <div class="nav-item">
                <a href="analytics_advanced.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Analytics
                </a>
            </div>
            <div class="nav-item">
                <a href="reports_advanced.php" class="nav-link">
                    <i class="fas fa-file-alt"></i>Reports
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="export.php" class="nav-link">
                    <i class="fas fa-download"></i>Export
                </a>
            </div>
            <div class="nav-item">
                <a href="profile_advanced.php" class="nav-link">
                    <i class="fas fa-user"></i>Profile
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Advanced Top Navbar -->
        <div class="top-navbar">
            <div>
                <h1 class="page-title">Lead Management</h1>
            </div>

            <div class="navbar-actions">
                <div class="search-box">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search leads by name, email, company..." id="globalSearch">
                </div>

                <div class="user-menu">
                    <div class="user-avatar pulse">
                        <?php echo strtoupper(substr($_SESSION['username'], 0, 1)); ?>
                    </div>
                    <a href="logout.php" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </a>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Success Messages -->
            <?php if(isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php
                    switch($_GET['success']) {
                        case 'added': echo 'Lead added successfully!'; break;
                        case 'updated': echo 'Lead updated successfully!'; break;
                        case 'deleted': echo 'Lead deleted successfully!'; break;
                        case 'bulk_updated': echo 'Leads updated successfully!'; break;
                    }
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Advanced Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card primary animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Total Leads</div>
                            <div class="stat-value"><?php echo $totalLeads; ?></div>
                            <div class="stat-label">All time</div>
                        </div>
                        <div class="stat-icon primary float">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card success animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Converted</div>
                            <div class="stat-value"><?php echo $statusCounts['converted']; ?></div>
                            <div class="stat-label">Revenue generating</div>
                        </div>
                        <div class="stat-icon success float">
                            <i class="fas fa-trophy"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card warning animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Hot Leads</div>
                            <div class="stat-value"><?php echo $statusCounts['hot']; ?></div>
                            <div class="stat-label">High priority</div>
                        </div>
                        <div class="stat-icon warning float">
                            <i class="fas fa-fire"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card danger animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">New Leads</div>
                            <div class="stat-value"><?php echo $statusCounts['new']; ?></div>
                            <div class="stat-label">Needs attention</div>
                        </div>
                        <div class="stat-icon danger float">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card info animate__animated animate__fadeInUp" style="animation-delay: 0.5s;">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Qualified</div>
                            <div class="stat-value"><?php echo $statusCounts['qualified']; ?></div>
                            <div class="stat-label">Ready to convert</div>
                        </div>
                        <div class="stat-icon info float">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Leads Table -->
            <div class="table-container animate__animated animate__fadeInUp" style="animation-delay: 0.6s;">
                <div class="table-header">
                    <div>
                        <h3 class="table-title">All Leads</h3>
                        <p class="text-muted mb-0">Manage and track your leads effectively</p>
                    </div>
                    <div class="table-actions">
                        <button class="btn btn-primary-modern" data-bs-toggle="modal" data-bs-target="#addLeadModal">
                            <i class="fas fa-plus"></i>Add Lead
                        </button>
                        <button class="btn btn-success-modern" onclick="bulkAction()">
                            <i class="fas fa-edit"></i>Bulk Edit
                        </button>
                        <a href="export.php" class="btn btn-warning-modern">
                            <i class="fas fa-download"></i>Export
                        </a>
                        <button class="btn btn-danger-modern" onclick="deleteSelected()">
                            <i class="fas fa-trash"></i>Delete Selected
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table" id="leadsTable">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Lead Info</th>
                                <th>Contact</th>
                                <th>Company</th>
                                <th>Service</th>
                                <th>Status</th>
                                <th>Priority</th>
                                <th>Source</th>
                                <th>Value</th>
                                <th>Created</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($leads as $lead): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input lead-checkbox" value="<?php echo $lead['id']; ?>">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-3" style="width: 40px; height: 40px; font-size: 0.9rem;">
                                            <?php echo strtoupper(substr($lead['name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($lead['name'] ?? ''); ?></div>
                                            <small class="text-muted">ID: <?php echo $lead['id']; ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-semibold"><?php echo htmlspecialchars($lead['email'] ?? ''); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($lead['phone'] ?? ''); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-semibold"><?php echo htmlspecialchars($lead['company'] ?? 'N/A'); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark"><?php echo ucfirst(str_replace('-', ' ', $lead['service'] ?? 'N/A')); ?></span>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $lead['status'] ?? 'new'; ?>">
                                        <i class="fas fa-circle"></i>
                                        <?php echo ucfirst($lead['status'] ?? 'new'); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="priority-badge priority-<?php echo $lead['priority'] ?? 'medium'; ?>">
                                        <?php echo ucfirst($lead['priority'] ?? 'medium'); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo ucfirst(str_replace('-', ' ', $lead['source'] ?? 'manual')); ?></span>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">$<?php echo number_format($lead['estimated_value'] ?? 0); ?></span>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-semibold"><?php echo date('M j, Y', strtotime($lead['created_at'] ?? 'now')); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($lead['created_at'] ?? 'now')); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-action btn-view" onclick="viewLead(<?php echo htmlspecialchars(json_encode($lead)); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-action btn-edit" onclick="editLead(<?php echo htmlspecialchars(json_encode($lead)); ?>)" title="Edit Lead">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-action btn-delete" onclick="deleteLead('<?php echo $lead['id']; ?>')" title="Delete Lead">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Add Lead Modal -->
    <div class="modal fade" id="addLeadModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>Add New Lead</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="addLeadForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row">
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Full Name *</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Email Address *</label>
                                            <input type="email" class="form-control" name="email" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Company</label>
                                            <input type="text" class="form-control" name="company">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Service Interest</label>
                                            <select class="form-select" name="service">
                                                <option value="">Select Service</option>
                                                <option value="web-development">Web Development</option>
                                                <option value="mobile-app">Mobile App Development</option>
                                                <option value="digital-marketing">Digital Marketing</option>
                                                <option value="consulting">Business Consulting</option>
                                                <option value="e-commerce">E-commerce Solutions</option>
                                                <option value="ui-ux-design">UI/UX Design</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Estimated Value</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" name="estimated_value" min="0" step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label fw-bold">Notes</label>
                                    <textarea class="form-control" name="notes" rows="4" placeholder="Additional information about this lead..."></textarea>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="bg-light p-3 rounded">
                                    <h6 class="fw-bold mb-3">Lead Classification</h6>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Status</label>
                                        <select class="form-select" name="status">
                                            <option value="new">New</option>
                                            <option value="contacted">Contacted</option>
                                            <option value="qualified">Qualified</option>
                                            <option value="hot">Hot</option>
                                            <option value="converted">Converted</option>
                                            <option value="lost">Lost</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Priority</label>
                                        <select class="form-select" name="priority">
                                            <option value="low">Low</option>
                                            <option value="medium" selected>Medium</option>
                                            <option value="high">High</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Source</label>
                                        <select class="form-select" name="source">
                                            <option value="manual">Manual Entry</option>
                                            <option value="website">Website</option>
                                            <option value="social-media">Social Media</option>
                                            <option value="referral">Referral</option>
                                            <option value="advertisement">Advertisement</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <small>This lead will be assigned to you automatically.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-primary-modern">
                            <i class="fas fa-save me-2"></i>Add Lead
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Advanced Edit Lead Modal -->
    <div class="modal fade" id="editLeadModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Lead</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editLeadForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="lead_id" id="edit_lead_id">

                        <div class="row">
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Full Name *</label>
                                            <input type="text" class="form-control" name="name" id="edit_name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Email Address *</label>
                                            <input type="email" class="form-control" name="email" id="edit_email" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone" id="edit_phone">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Company</label>
                                            <input type="text" class="form-control" name="company" id="edit_company">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Service Interest</label>
                                            <select class="form-select" name="service" id="edit_service">
                                                <option value="">Select Service</option>
                                                <option value="web-development">Web Development</option>
                                                <option value="mobile-app">Mobile App Development</option>
                                                <option value="digital-marketing">Digital Marketing</option>
                                                <option value="consulting">Business Consulting</option>
                                                <option value="e-commerce">E-commerce Solutions</option>
                                                <option value="ui-ux-design">UI/UX Design</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Estimated Value</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" name="estimated_value" id="edit_estimated_value" min="0" step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label fw-bold">Notes</label>
                                    <textarea class="form-control" name="notes" id="edit_notes" rows="4"></textarea>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="bg-light p-3 rounded">
                                    <h6 class="fw-bold mb-3">Lead Classification</h6>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Status</label>
                                        <select class="form-select" name="status" id="edit_status">
                                            <option value="new">New</option>
                                            <option value="contacted">Contacted</option>
                                            <option value="qualified">Qualified</option>
                                            <option value="hot">Hot</option>
                                            <option value="converted">Converted</option>
                                            <option value="lost">Lost</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Priority</label>
                                        <select class="form-select" name="priority" id="edit_priority">
                                            <option value="low">Low</option>
                                            <option value="medium">Medium</option>
                                            <option value="high">High</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Source</label>
                                        <select class="form-select" name="source" id="edit_source">
                                            <option value="manual">Manual Entry</option>
                                            <option value="website">Website</option>
                                            <option value="social-media">Social Media</option>
                                            <option value="referral">Referral</option>
                                            <option value="advertisement">Advertisement</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>

                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <small>Changes will be saved immediately.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-warning-modern">
                            <i class="fas fa-save me-2"></i>Update Lead
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Lead Modal -->
    <div class="modal fade" id="viewLeadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Lead Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="viewLeadContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary-modern" onclick="editFromView()">
                        <i class="fas fa-edit me-2"></i>Edit Lead
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Action Modal -->
    <div class="modal fade" id="bulkActionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Bulk Update</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="bulkActionForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="bulk_update">
                        <div id="selectedLeadsInput"></div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Update Status</label>
                            <select class="form-select" name="bulk_status" required>
                                <option value="">Select New Status</option>
                                <option value="new">New</option>
                                <option value="contacted">Contacted</option>
                                <option value="qualified">Qualified</option>
                                <option value="hot">Hot</option>
                                <option value="converted">Converted</option>
                                <option value="lost">Lost</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="bulkActionCount">0</span> leads will be updated.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success-modern">
                            <i class="fas fa-save me-2"></i>Update Selected
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/select/1.7.0/js/dataTables.select.min.js"></script>
    <script>
        let currentLead = null;
        let leadsTable;

        // Initialize Advanced DataTable
        $(document).ready(function() {
            leadsTable = $('#leadsTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[9, 'desc']], // Sort by created date
                columnDefs: [
                    { orderable: false, targets: [0, 10] }, // Disable sorting on checkbox and actions
                    { searchable: false, targets: [0, 10] }
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                language: {
                    search: "Search leads:",
                    lengthMenu: "Show _MENU_ leads per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ leads",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                },
                drawCallback: function() {
                    // Re-apply animations to new rows
                    $('.table tbody tr').addClass('animate__animated animate__fadeIn');
                }
            });

            // Global search functionality
            $('#globalSearch').on('keyup', function() {
                leadsTable.search(this.value).draw();
            });

            // Select all functionality
            $('#selectAll').on('change', function() {
                $('.lead-checkbox').prop('checked', this.checked);
                updateBulkActionButton();
            });

            // Individual checkbox functionality
            $(document).on('change', '.lead-checkbox', function() {
                updateBulkActionButton();

                // Update select all checkbox
                const totalCheckboxes = $('.lead-checkbox').length;
                const checkedCheckboxes = $('.lead-checkbox:checked').length;
                $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
            });
        });

        // Update bulk action button state
        function updateBulkActionButton() {
            const selectedCount = $('.lead-checkbox:checked').length;
            const bulkBtn = $('button[onclick="bulkAction()"]');
            const deleteBtn = $('button[onclick="deleteSelected()"]');

            if (selectedCount > 0) {
                bulkBtn.removeClass('btn-success-modern').addClass('btn-warning-modern');
                bulkBtn.html(`<i class="fas fa-edit"></i>Bulk Edit (${selectedCount})`);
                deleteBtn.removeClass('btn-danger-modern').addClass('btn-outline-danger');
                deleteBtn.html(`<i class="fas fa-trash"></i>Delete (${selectedCount})`);
            } else {
                bulkBtn.removeClass('btn-warning-modern').addClass('btn-success-modern');
                bulkBtn.html('<i class="fas fa-edit"></i>Bulk Edit');
                deleteBtn.removeClass('btn-outline-danger').addClass('btn-danger-modern');
                deleteBtn.html('<i class="fas fa-trash"></i>Delete Selected');
            }
        }

        // View lead function
        function viewLead(lead) {
            currentLead = lead;

            const content = `
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-user me-2"></i>Contact Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>Name:</strong><br>
                                        <span class="text-muted">${lead.name || 'N/A'}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Email:</strong><br>
                                        <span class="text-muted">${lead.email || 'N/A'}</span>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>Phone:</strong><br>
                                        <span class="text-muted">${lead.phone || 'N/A'}</span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Company:</strong><br>
                                        <span class="text-muted">${lead.company || 'N/A'}</span>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <strong>Service Interest:</strong><br>
                                        <span class="badge bg-primary">${lead.service ? lead.service.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'N/A'}</span>
                                    </div>
                                </div>
                                ${lead.notes ? `
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <strong>Notes:</strong><br>
                                        <span class="text-muted">${lead.notes}</span>
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Lead Status</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Status:</strong><br>
                                    <span class="status-badge status-${lead.status || 'new'}">
                                        <i class="fas fa-circle"></i>
                                        ${(lead.status || 'new').charAt(0).toUpperCase() + (lead.status || 'new').slice(1)}
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <strong>Priority:</strong><br>
                                    <span class="priority-badge priority-${lead.priority || 'medium'}">
                                        ${(lead.priority || 'medium').charAt(0).toUpperCase() + (lead.priority || 'medium').slice(1)}
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <strong>Source:</strong><br>
                                    <span class="badge bg-secondary">${lead.source ? lead.source.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Manual'}</span>
                                </div>
                                <div class="mb-3">
                                    <strong>Estimated Value:</strong><br>
                                    <span class="fw-bold text-success">$${lead.estimated_value ? Number(lead.estimated_value).toLocaleString() : '0'}</span>
                                </div>
                                <div class="mb-0">
                                    <strong>Created:</strong><br>
                                    <span class="text-muted">${new Date(lead.created_at || Date.now()).toLocaleDateString()}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('viewLeadContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('viewLeadModal')).show();
        }

        // Edit from view modal
        function editFromView() {
            bootstrap.Modal.getInstance(document.getElementById('viewLeadModal')).hide();
            setTimeout(() => editLead(currentLead), 300);
        }

        // Edit lead function
        function editLead(lead) {
            currentLead = lead;

            document.getElementById('edit_lead_id').value = lead.id;
            document.getElementById('edit_name').value = lead.name || '';
            document.getElementById('edit_email').value = lead.email || '';
            document.getElementById('edit_phone').value = lead.phone || '';
            document.getElementById('edit_company').value = lead.company || '';
            document.getElementById('edit_service').value = lead.service || '';
            document.getElementById('edit_status').value = lead.status || 'new';
            document.getElementById('edit_priority').value = lead.priority || 'medium';
            document.getElementById('edit_source').value = lead.source || 'manual';
            document.getElementById('edit_estimated_value').value = lead.estimated_value || '';
            document.getElementById('edit_notes').value = lead.notes || '';

            new bootstrap.Modal(document.getElementById('editLeadModal')).show();
        }

        // Delete lead function
        function deleteLead(leadId) {
            if(confirm('Are you sure you want to delete this lead? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="lead_id" value="${leadId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Bulk action function
        function bulkAction() {
            const selectedLeads = $('.lead-checkbox:checked');
            if (selectedLeads.length === 0) {
                alert('Please select at least one lead to update.');
                return;
            }

            // Populate hidden inputs for selected leads
            const inputsHtml = Array.from(selectedLeads).map(checkbox =>
                `<input type="hidden" name="lead_ids[]" value="${checkbox.value}">`
            ).join('');

            document.getElementById('selectedLeadsInput').innerHTML = inputsHtml;
            document.getElementById('bulkActionCount').textContent = selectedLeads.length;

            new bootstrap.Modal(document.getElementById('bulkActionModal')).show();
        }

        // Delete selected function
        function deleteSelected() {
            const selectedLeads = $('.lead-checkbox:checked');
            if (selectedLeads.length === 0) {
                alert('Please select at least one lead to delete.');
                return;
            }

            if(confirm(`Are you sure you want to delete ${selectedLeads.length} selected leads? This action cannot be undone.`)) {
                selectedLeads.each(function() {
                    deleteLead(this.value);
                });
            }
        }

        // Animate statistics on page load
        document.addEventListener('DOMContentLoaded', function() {
            const stats = document.querySelectorAll('.stat-value');
            stats.forEach((stat, index) => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = finalValue / 30;

                setTimeout(() => {
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            stat.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            stat.textContent = Math.floor(currentValue);
                        }
                    }, 50);
                }, index * 200);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + N = New Lead
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                new bootstrap.Modal(document.getElementById('addLeadModal')).show();
            }
        });
    </script>
</body>
</html>
